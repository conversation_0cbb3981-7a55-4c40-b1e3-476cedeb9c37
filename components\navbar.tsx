"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { FileText, ChevronRight, User, Code, FolderOpen, Briefcase, Mail } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"

export function Navbar() {
  const [scrolled, setScrolled] = useState(false)
  const [activeSection, setActiveSection] = useState("home")
  const [showNav, setShowNav] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 100

      setShowNav(true)
      setScrolled(window.scrollY > 10)

      const sections = document.querySelectorAll("section[id]")

      sections.forEach((section) => {
        const sectionTop = (section as HTMLElement).offsetTop
        const sectionHeight = (section as HTMLElement).offsetHeight
        const sectionId = section.getAttribute("id") || ""

        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
          setActiveSection(sectionId)
        }
      })
    }

    setShowNav(true)
    handleScroll()

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const navLinks = [
    { name: "About", href: "#about", icon: User },
    { name: "Skills", href: "#skills", icon: Code },
    { name: "Projects", href: "#projects", icon: FolderOpen },
    { name: "Experience", href: "#experience", icon: Briefcase },
    { name: "Contact", href: "#contact", icon: Mail },
  ]

  return (
    <header
      className={cn(
        "fixed top-0 w-full z-50 transition-all duration-500 flex justify-center",
        showNav ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-full",
        scrolled ? "pt-4" : "pt-6"
      )}
    >
      {/* Desktop Navigation */}
      <nav className={cn(
        "hidden md:flex items-center px-6 py-3 rounded-full transition-all duration-500",
        "bg-black/20 backdrop-blur-xl border border-white/10",
        "shadow-lg shadow-black/20",
        scrolled
          ? "bg-black/30 border-white/20"
          : "bg-black/10 border-white/5"
      )}>
        <div className="flex items-center space-x-1">
          {navLinks.map((link) => (
            <Link
              key={link.name}
              href={link.href}
              className={cn(
                "px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 relative group",
                activeSection === link.href.substring(1)
                  ? "text-white bg-white/10 shadow-lg"
                  : "text-white/70 hover:text-white hover:bg-white/5"
              )}
            >
              {activeSection === link.href.substring(1) && (
                <motion.span
                  layoutId="navbar-active-pill"
                  className="absolute inset-0 bg-white/10 rounded-full -z-10 shadow-lg"
                  transition={{ type: "spring", duration: 0.6 }}
                />
              )}
              {link.name}
            </Link>
          ))}

          <div className="w-px h-6 bg-white/20 mx-2" />

          <Button
            size="sm"
            className="bg-gradient-to-r from-primary/80 to-blue-500/80 hover:from-primary hover:to-blue-500 text-white group transition-all duration-300 border-none rounded-full px-4 py-2 shadow-lg"
            onClick={() => window.open("https://drive.google.com/file/d/1Hojq4Hx1jY4n0GnkjFnmm8lQWNXj2Coy/view", "_blank")}
          >
            <FileText size={14} className="mr-1" />
            Resume
            <ChevronRight size={14} className="ml-1 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
          </Button>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <nav className={cn(
        "md:hidden fixed left-4 top-1/2 -translate-y-1/2 z-50 transition-all duration-500",
        "bg-black/20 backdrop-blur-xl border border-white/10",
        "shadow-lg shadow-black/20 rounded-2xl p-3",
        scrolled
          ? "bg-black/30 border-white/20"
          : "bg-black/10 border-white/5"
      )}>
        <div className="flex flex-col items-center space-y-3">
          {navLinks.map((link) => {
            const IconComponent = link.icon
            return (
              <Link
                key={link.name}
                href={link.href}
                className={cn(
                  "p-3 rounded-xl transition-all duration-300 relative group",
                  activeSection === link.href.substring(1)
                    ? "text-white bg-white/10 shadow-lg"
                    : "text-white/70 hover:text-white hover:bg-white/5"
                )}
              >
                {activeSection === link.href.substring(1) && (
                  <motion.span
                    layoutId="mobile-navbar-active-pill"
                    className="absolute inset-0 bg-white/10 rounded-xl -z-10 shadow-lg"
                    transition={{ type: "spring", duration: 0.6 }}
                  />
                )}
                <IconComponent size={20} />
              </Link>
            )
          })}

          <div className="w-6 h-px bg-white/20 my-1" />

          <Button
            size="sm"
            className="bg-gradient-to-r from-primary/80 to-blue-500/80 hover:from-primary hover:to-blue-500 text-white group transition-all duration-300 border-none rounded-xl p-3 shadow-lg"
            onClick={() => window.open("https://drive.google.com/file/d/1Hojq4Hx1jY4n0GnkjFnmm8lQWNXj2Coy/view", "_blank")}
          >
            <FileText size={20} />
          </Button>
        </div>
      </nav>
    </header>
  )
}
