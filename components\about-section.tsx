"use client"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { FileText, GraduationCap, MapPin, Mail, Briefcase, ChevronRight } from "lucide-react"

export function AboutSection() {
  return (
    <section id="about" className="py-24 relative overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-l from-purple-500/10 to-primary/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold mb-4 neo-text font-space-grotesk bg-clip-text text-transparent bg-gradient-to-r from-white via-blue-100 to-purple-200">
            About Me
          </h2>
          <div className="w-24 h-1.5 bg-gradient-to-r from-primary to-blue-500 mx-auto mb-6 neo-glow rounded-full"></div>
          <p className="text-white/70 max-w-2xl mx-auto text-lg font-poppins">
            Passionate developer crafting digital experiences with modern technologies
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-5 gap-12 items-start">
            {/* Professional Image Section */}
            <div className="lg:col-span-2 flex justify-center">
              <div className="relative group">
                {/* Main Image Container */}
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary/20 to-purple-500/20 p-1">
                  <div className="w-80 h-96 rounded-xl overflow-hidden bg-black/20 backdrop-blur-sm">
                    <Image
                      src="/profile.png"
                      alt="Zaid Ahmed S - Full Stack Developer"
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-105"
                    />
                  </div>
                </div>

                {/* Floating Tech Elements */}
                <div className="absolute -top-6 -right-6 w-12 h-12 bg-gradient-to-r from-primary to-blue-500 rounded-full neo-glow animate-pulse flex items-center justify-center">
                  <span className="text-white text-xs font-bold">JS</span>
                </div>
                <div className="absolute -bottom-6 -left-6 w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full neo-glow animate-pulse animation-delay-1000 flex items-center justify-center">
                  <span className="text-white text-xs font-bold">⚛</span>
                </div>
                <div className="absolute top-1/2 -left-8 w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full neo-glow animate-pulse animation-delay-2000 flex items-center justify-center">
                  <span className="text-white text-xs font-bold">N</span>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="lg:col-span-3 space-y-8">
              {/* Introduction */}
              <div className="space-y-6">
                <h3 className="text-2xl font-semibold text-white font-space-grotesk">
                  Full-Stack Developer & AI Enthusiast
                </h3>
                <p className="text-white/90 text-lg leading-relaxed font-poppins">
                  I'm a passionate full-stack developer with expertise in modern web technologies including React, Next.js, Node.js, and MongoDB. Currently pursuing my MCA at Measi Institute of Information Technology, I focus on building scalable, user-centric applications while exploring the exciting realms of cloud computing and DevOps.
                </p>
                <p className="text-white/80 leading-relaxed font-poppins">
                  My journey in technology is driven by curiosity and a commitment to continuous learning. I believe in writing clean, efficient code and creating digital experiences that make a difference.
                </p>
              </div>

              {/* Professional Info Grid */}
              <div className="grid sm:grid-cols-2 gap-6">
                <div className="group nebula-card p-6 hover:border-primary/30 transition-all duration-300">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary/30 transition-colors">
                      <GraduationCap className="w-5 h-5 text-primary" />
                    </div>
                    <h3 className="font-semibold text-primary font-space-grotesk">Education</h3>
                  </div>
                  <p className="text-white/90 font-medium font-poppins">MCA (Ongoing)</p>
                  <p className="text-white/70 text-sm font-poppins">Measi Institute of Information Technology</p>
                </div>

                <div className="group nebula-card p-6 hover:border-primary/30 transition-all duration-300">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary/30 transition-colors">
                      <MapPin className="w-5 h-5 text-primary" />
                    </div>
                    <h3 className="font-semibold text-primary font-space-grotesk">Location</h3>
                  </div>
                  <p className="text-white/90 font-medium font-poppins">Chennai, India</p>
                  <p className="text-white/70 text-sm font-poppins">Available for remote work</p>
                </div>

                <div className="group nebula-card p-6 hover:border-primary/30 transition-all duration-300">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary/30 transition-colors">
                      <Mail className="w-5 h-5 text-primary" />
                    </div>
                    <h3 className="font-semibold text-primary font-space-grotesk">Email</h3>
                  </div>
                  <p className="text-white/90 font-medium font-poppins"><EMAIL></p>
                  <p className="text-white/70 text-sm font-poppins">Let's connect and collaborate</p>
                </div>

                <div className="group nebula-card p-6 hover:border-primary/30 transition-all duration-300">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary/30 transition-colors">
                      <Briefcase className="w-5 h-5 text-primary" />
                    </div>
                    <h3 className="font-semibold text-primary font-space-grotesk">Availability</h3>
                  </div>
                  <p className="text-white/90 font-medium font-poppins">Open to Opportunities</p>
                  <p className="text-white/70 text-sm font-poppins">Full-time & freelance projects</p>
                </div>
              </div>

              {/* CTA Button */}
              <div className="pt-4">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-primary/20 to-blue-500/20 border border-primary/50 hover:from-primary/30 hover:to-blue-500/30 text-white neo-glow group transition-all duration-300 shadow-lg hover:shadow-primary/25"
                  onClick={() => window.open("https://drive.google.com/file/d/1Hojq4Hx1jY4n0GnkjFnmm8lQWNXj2Coy/view?usp=sharing", "_blank")}
                >
                  <FileText className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
                  Download Resume
                  <ChevronRight className="ml-2 h-4 w-4 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

