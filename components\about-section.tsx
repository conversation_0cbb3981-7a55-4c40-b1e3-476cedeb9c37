"use client"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { FileText, GraduationCap, MapPin, Mail, Briefcase, ChevronRight } from "lucide-react"

export function AboutSection() {
  return (
    <section id="about" className="py-24 relative overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-l from-purple-500/10 to-primary/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold mb-4 neo-text font-space-grotesk bg-clip-text text-transparent bg-gradient-to-r from-white via-blue-100 to-purple-200">
            About Me
          </h2>
          <div className="w-24 h-1.5 bg-gradient-to-r from-primary to-blue-500 mx-auto mb-6 neo-glow rounded-full"></div>
          <p className="text-white/70 max-w-2xl mx-auto text-lg font-poppins">
            Passionate developer crafting digital experiences with modern technologies
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-20 items-center">
            {/* Content Section - Left Side */}
            <div className="order-2 lg:order-1 space-y-8">
              {/* Main Title */}
              <div className="space-y-6">
                <h3 className="text-4xl lg:text-5xl font-bold text-white font-space-grotesk leading-tight">
                  I'm Zaid, I create{' '}
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-blue-400">
                    scalable and innovative
                  </span>{' '}
                  digital experiences.
                </h3>

                <div className="space-y-4">
                  <p className="text-white/80 text-lg leading-relaxed font-poppins">
                    With expertise in modern web technologies including React, Next.js, Node.js, and MongoDB, I focus on building user-centric applications that bridge the gap between complex functionality and intuitive design.
                  </p>
                  <p className="text-white/70 leading-relaxed font-poppins">
                    Currently pursuing MCA at Measi Institute of Information Technology, I'm passionate about exploring cloud computing, DevOps, and AI automation to create solutions that make a difference.
                  </p>
                </div>
              </div>

              {/* CTA Button */}
              <div className="pt-2">
                <Button
                  size="lg"
                  className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg font-semibold rounded-lg transition-all duration-300 shadow-lg hover:shadow-primary/25 hover:scale-105"
                  onClick={() => window.open("https://drive.google.com/file/d/1Hojq4Hx1jY4n0GnkjFnmm8lQWNXj2Coy/view?usp=sharing", "_blank")}
                >
                  <FileText className="mr-2 h-5 w-5" />
                  Resume
                </Button>
              </div>
            </div>

            {/* Professional Image Section - Right Side */}
            <div className="order-1 lg:order-2 flex justify-center lg:justify-end">
              <div className="relative">
                {/* Background Gradient Circle */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-blue-500/20 to-purple-500/30 rounded-full blur-2xl scale-110"></div>

                {/* Main Image Container */}
                <div className="relative w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-white/10 shadow-2xl">
                  <Image
                    src="/profile.png"
                    alt="Zaid Ahmed S - Full Stack Developer"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Professional Info Cards - Bottom Section */}
          <div className="mt-20 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="nebula-card p-6 text-center hover:border-primary/30 transition-all duration-300 group">
              <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/30 transition-colors">
                <GraduationCap className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-semibold text-primary font-space-grotesk mb-2">Education</h3>
              <p className="text-white/90 font-medium font-poppins text-sm">MCA (Ongoing)</p>
              <p className="text-white/70 text-xs font-poppins">Measi Institute of Information Technology</p>
            </div>

            <div className="nebula-card p-6 text-center hover:border-primary/30 transition-all duration-300 group">
              <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/30 transition-colors">
                <MapPin className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-semibold text-primary font-space-grotesk mb-2">Location</h3>
              <p className="text-white/90 font-medium font-poppins text-sm">Chennai, India</p>
              <p className="text-white/70 text-xs font-poppins">Available for remote work</p>
            </div>

            <div className="nebula-card p-6 text-center hover:border-primary/30 transition-all duration-300 group">
              <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/30 transition-colors">
                <Mail className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-semibold text-primary font-space-grotesk mb-2">Email</h3>
              <p className="text-white/90 font-medium font-poppins text-sm"><EMAIL></p>
              <p className="text-white/70 text-xs font-poppins">Let's connect and collaborate</p>
            </div>

            <div className="nebula-card p-6 text-center hover:border-primary/30 transition-all duration-300 group">
              <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/30 transition-colors">
                <Briefcase className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-semibold text-primary font-space-grotesk mb-2">Availability</h3>
              <p className="text-white/90 font-medium font-poppins text-sm">Open to Opportunities</p>
              <p className="text-white/70 text-xs font-poppins">Full-time & freelance projects</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

