"use client"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Globe, Layout, Smartphone, Zap, Cpu, PenTool, Code, Server, Database } from "lucide-react"
import { motion } from "framer-motion"

export function SkillsSection() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)
  const [isInView, setIsInView] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  }
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 100 },
    },
  }
  
  // Check if section is in view
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
        }
      },
      { threshold: 0.1 }
    )
    
    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }
    
    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current)
      }
    }
  }, [])

  const skills = [
    {
      category: "Frontend",
      icon: <Layout className="w-10 h-10 text-primary" />,
      technologies: ["HTML", "CSS", "JavaScript", "React", "Responsive Design"],
    },
    {
      category: "UI/UX",
      icon: <PenTool className="w-10 h-10 text-primary" />,
      technologies: ["Figma", "User Research", "Wireframing", "Prototyping"],
    },
    {
      category: "Tools",
      icon: <Zap className="w-10 h-10 text-primary" />,
      technologies: ["Git", "Power BI", "VS Code", "Chrome DevTools"],
    },
    {
      category: "Learning",
      icon: <Cpu className="w-10 h-10 text-primary" />,
      technologies: ["Node.js", "Express", "MongoDB", "DevOps", "AWS"],
    },
    {
      category: "Soft Skills",
      icon: <Globe className="w-10 h-10 text-primary" />,
      technologies: ["Problem Solving", "Team Collaboration", "Communication", "Time Management"],
    },
    {
      category: "Other",
      icon: <Smartphone className="w-10 h-10 text-primary" />,
      technologies: ["Prompt Engineering", "SEO Basics", "Performance Optimization", "Accessibility"],
    },
  ]

  return (
    <section id="skills" className="py-20 relative overflow-hidden" ref={sectionRef}>
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 opacity-30">
        <div className="absolute top-0 left-0 w-1/3 h-1/3 bg-gradient-to-br from-primary/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-1/3 h-1/3 bg-gradient-to-tr from-blue-500/10 to-transparent rounded-full blur-3xl"></div>
      </div>
      
      <div className="container mx-auto px-4">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-3 neo-text bg-clip-text text-transparent bg-gradient-to-r from-white to-white/90 font-space-grotesk">
            My Skills
          </h2>
          <div className="w-24 h-1.5 bg-gradient-to-r from-primary to-blue-500 mx-auto mb-6 neo-glow rounded-full"></div>
          <p className="text-white/80 max-w-2xl mx-auto text-lg">
            I've developed expertise in various technologies and continue to expand my knowledge in new areas.
          </p>
        </motion.div>

        {/* Tree Structure Container */}
        <div className="relative max-w-6xl mx-auto">
          {/* SVG for tree branches */}
          <svg
            className="absolute inset-0 w-full h-full pointer-events-none z-0"
            viewBox="0 0 1200 800"
            preserveAspectRatio="xMidYMid meet"
          >
            {/* Branch lines - will be animated */}
            <defs>
              <linearGradient id="branchGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.6" />
                <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.3" />
              </linearGradient>
              <linearGradient id="branchGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.6" />
                <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.3" />
              </linearGradient>
              <linearGradient id="branchGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.6" />
                <stop offset="100%" stopColor="#10b981" stopOpacity="0.3" />
              </linearGradient>
            </defs>

            {/* Branch paths from center to each card position */}
            {/* Frontend Branch */}
            <motion.path
              d="M 600 80 Q 400 200 200 300"
              stroke={hoveredIndex === 0 ? "#3b82f6" : "url(#branchGradient1)"}
              strokeWidth={hoveredIndex === 0 ? "3" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
              className={hoveredIndex === 0 ? "drop-shadow-[0_0_8px_rgba(59,130,246,0.6)]" : ""}
            />
            {/* UI/UX Branch */}
            <motion.path
              d="M 600 80 Q 600 200 600 300"
              stroke={hoveredIndex === 1 ? "#8b5cf6" : "url(#branchGradient2)"}
              strokeWidth={hoveredIndex === 1 ? "3" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1, delay: 0.7 }}
              className={hoveredIndex === 1 ? "drop-shadow-[0_0_8px_rgba(139,92,246,0.6)]" : ""}
            />
            {/* Tools Branch */}
            <motion.path
              d="M 600 80 Q 800 200 1000 300"
              stroke={hoveredIndex === 2 ? "#06b6d4" : "url(#branchGradient3)"}
              strokeWidth={hoveredIndex === 2 ? "3" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1, delay: 0.9 }}
              className={hoveredIndex === 2 ? "drop-shadow-[0_0_8px_rgba(6,182,212,0.6)]" : ""}
            />
            {/* Learning Branch */}
            <motion.path
              d="M 600 80 Q 350 350 200 500"
              stroke={hoveredIndex === 3 ? "#3b82f6" : "url(#branchGradient1)"}
              strokeWidth={hoveredIndex === 3 ? "3" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1, delay: 1.1 }}
              className={hoveredIndex === 3 ? "drop-shadow-[0_0_8px_rgba(59,130,246,0.6)]" : ""}
            />
            {/* Soft Skills Branch */}
            <motion.path
              d="M 600 80 Q 600 350 600 500"
              stroke={hoveredIndex === 4 ? "#8b5cf6" : "url(#branchGradient2)"}
              strokeWidth={hoveredIndex === 4 ? "3" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1, delay: 1.3 }}
              className={hoveredIndex === 4 ? "drop-shadow-[0_0_8px_rgba(139,92,246,0.6)]" : ""}
            />
            {/* Other Branch */}
            <motion.path
              d="M 600 80 Q 850 350 1000 500"
              stroke={hoveredIndex === 5 ? "#06b6d4" : "url(#branchGradient3)"}
              strokeWidth={hoveredIndex === 5 ? "3" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1, delay: 1.5 }}
              className={hoveredIndex === 5 ? "drop-shadow-[0_0_8px_rgba(6,182,212,0.6)]" : ""}
            />

            {/* Floating particles along branches */}
            {isInView && (
              <>
                <motion.circle
                  cx="400"
                  cy="200"
                  r="2"
                  fill="#3b82f6"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [600, 400, 200],
                    cy: [80, 200, 300]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: 2,
                    ease: "easeInOut"
                  }}
                />
                <motion.circle
                  cx="600"
                  cy="200"
                  r="1.5"
                  fill="#8b5cf6"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cy: [80, 200, 300]
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    delay: 3,
                    ease: "easeInOut"
                  }}
                />
                <motion.circle
                  cx="800"
                  cy="200"
                  r="2"
                  fill="#06b6d4"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [600, 800, 1000],
                    cy: [80, 200, 300]
                  }}
                  transition={{
                    duration: 3.5,
                    repeat: Infinity,
                    delay: 4,
                    ease: "easeInOut"
                  }}
                />
              </>
            )}
          </svg>

          {/* Skills Cards in Tree Layout */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="relative z-10"
          >
            {/* Top Row - 3 cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {skills.slice(0, 3).map((skill, index) => (
                <motion.div key={index} variants={itemVariants} className="flex justify-center">
                  <div className="w-full max-w-sm">
                    <Card
                      className={`group relative overflow-hidden transition-all duration-500 ${
                        hoveredIndex === index
                          ? "neo-glow transform -translate-y-2 scale-105"
                          : "hover:transform hover:-translate-y-1"
                      }`}
                      onMouseEnter={() => setHoveredIndex(index)}
                      onMouseLeave={() => setHoveredIndex(null)}
                    >
                      {/* Card background with gradient border effect */}
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/30 to-blue-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                      <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center">
                            <div
                              className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === index ? "scale-110 neo-glow" : ""}`}
                            >
                              {skill.icon}
                            </div>
                            <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-primary transition-colors duration-300 font-space-grotesk">
                              {skill.category}
                            </h3>
                            <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                              {skill.technologies.map((tech, techIndex) => (
                                <span
                                  key={techIndex}
                                  className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs border border-primary/20 transition-all duration-300 hover:bg-primary/20 hover:border-primary/40"
                                >
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Bottom Row - 3 cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {skills.slice(3, 6).map((skill, index) => (
                <motion.div key={index + 3} variants={itemVariants} className="flex justify-center">
                  <div className="w-full max-w-sm">
                    <Card
                      className={`group relative overflow-hidden transition-all duration-500 ${
                        hoveredIndex === index + 3
                          ? "neo-glow transform -translate-y-2 scale-105"
                          : "hover:transform hover:-translate-y-1"
                      }`}
                      onMouseEnter={() => setHoveredIndex(index + 3)}
                      onMouseLeave={() => setHoveredIndex(null)}
                    >
                      {/* Card background with gradient border effect */}
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/30 to-blue-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                      <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center">
                            <div
                              className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === index + 3 ? "scale-110 neo-glow" : ""}`}
                            >
                              {skill.icon}
                            </div>
                            <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-primary transition-colors duration-300 font-space-grotesk">
                              {skill.category}
                            </h3>
                            <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                              {skill.technologies.map((tech, techIndex) => (
                                <span
                                  key={techIndex}
                                  className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs border border-primary/20 transition-all duration-300 hover:bg-primary/20 hover:border-primary/40"
                                >
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        </motion.div>
      </div>

      {/* Removed floating geometric shapes - galaxy background provides the cosmic effect */}
    </section>
  )
}

